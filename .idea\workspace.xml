<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="52cfcf32-1667-4a1b-bc36-2471a9ec9029" name="Changes" comment="now generating new token after plan cancellation">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/job/jobportal/config/DatabaseInitializer.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/job/jobportal/config/DatabaseInitializer.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/job/jobportal/repository/SEOPagesRepo.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/job/jobportal/repository/SEOPagesRepo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/job/jobportal/service/JobService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/job/jobportal/service/JobService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/job/jobportal/service/SEOPagesService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/job/jobportal/service/SEOPagesService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/job/jobportal/service/StripeService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/job/jobportal/service/StripeService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-prod.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-prod.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-qa.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-qa.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application-dev.properties" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application-dev.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/messages.properties" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/messages.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/schema.sql" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/schema.sql" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="bug/update-token-after-cancelation" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;lakshay202&quot;
  }
}</component>
  <component name="GithubDefaultAccount">
    <option name="defaultAccountId" value="245a7870-5b8b-4832-b5e6-83908ce561e6" />
  </component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/mrgamedev07/jobportalBackend.git&quot;,
    &quot;accountId&quot;: &quot;245a7870-5b8b-4832-b5e6-83908ce561e6&quot;
  },
  &quot;recentNewPullRequestHead&quot;: {
    &quot;server&quot;: {
      &quot;useHttp&quot;: false,
      &quot;host&quot;: &quot;github.com&quot;,
      &quot;port&quot;: null,
      &quot;suffix&quot;: null
    },
    &quot;owner&quot;: &quot;mrgamedev07&quot;,
    &quot;repository&quot;: &quot;jobportalBackend&quot;
  }
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2wRbctTDEo4hJg7M1lrUrOqPkSw" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.JobPortalApplication.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.All in jobportal (1).executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.All in jobportal (2).executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.All in jobportal.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/jobportalBackend&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Artifacts&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\jobportalBackend" />
    </key>
  </component>
  <component name="RunManager" selected="Application.JobPortalApplication">
    <configuration name="JobPortalApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.job.jobportal.JobPortalApplication" />
      <module name="jobportal" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.job.jobportal.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="All in jobportal (1)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="jobportal" />
      <option name="PACKAGE_NAME" value="" />
      <option name="TEST_OBJECT" value="package" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="All in jobportal (2)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="jobportal" />
      <option name="PACKAGE_NAME" value="" />
      <option name="TEST_OBJECT" value="package" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="All in jobportal" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="jobportal" />
      <option name="PACKAGE_NAME" value="" />
      <option name="TEST_OBJECT" value="package" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.JobPortalApplication" />
        <item itemvalue="JUnit.All in jobportal" />
        <item itemvalue="JUnit.All in jobportal (2)" />
        <item itemvalue="JUnit.All in jobportal (1)" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="52cfcf32-1667-4a1b-bc36-2471a9ec9029" name="Changes" comment="" />
      <created>1746007415759</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746007415759</updated>
    </task>
    <task id="LOCAL-00001" summary="Employer-Page-Country/district-and-city-is-not-displayed,-open-jobs-count-incorrect-and-remove-companies-heading-#281&#10;Add-Job-Subcategory-and-Job-Sub-Subcategory-to-Candidate-Profile-Page-#282">
      <option name="closed" value="true" />
      <created>1746013663243</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1746013663244</updated>
    </task>
    <task id="LOCAL-00002" summary="expose programmatic pages api search endpoint">
      <option name="closed" value="true" />
      <created>1746016125608</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1746016125608</updated>
    </task>
    <task id="LOCAL-00003" summary="job post api">
      <option name="closed" value="true" />
      <created>1746026564986</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1746026564986</updated>
    </task>
    <task id="LOCAL-00004" summary="changed subject from json node to string">
      <option name="closed" value="true" />
      <created>1746092805097</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1746092805097</updated>
    </task>
    <task id="LOCAL-00005" summary="Search Field - Search only return exact match #291">
      <option name="closed" value="true" />
      <created>1746167337749</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1746167337749</updated>
    </task>
    <task id="LOCAL-00006" summary="added qa and prod profiles">
      <option name="closed" value="true" />
      <created>1746169317205</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1746169317206</updated>
    </task>
    <task id="LOCAL-00007" summary="candidate profile api modified jobCategories">
      <option name="closed" value="true" />
      <created>1746187507537</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1746187507537</updated>
    </task>
    <task id="LOCAL-00008" summary="candidate profile api and candidate profile modified">
      <option name="closed" value="true" />
      <created>1746251079377</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1746251079377</updated>
    </task>
    <task id="LOCAL-00009" summary="job-count-in-company-page-send-email-to-candidate-when-applying-job-otp-email-no-need-for-login-link">
      <option name="closed" value="true" />
      <created>1746426191386</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746426191388</updated>
    </task>
    <task id="LOCAL-00010" summary="Applicant Page - Filter Not Working as Expected #301&#10;&#10;Email Notifications - Replace Full URLs with &quot;Click Here&quot; Links and Update Signature #300">
      <option name="closed" value="true" />
      <created>1746445984305</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1746445984306</updated>
    </task>
    <task id="LOCAL-00011" summary="Applicant Page - Filter Not Working as Expected #301">
      <option name="closed" value="true" />
      <created>1746508586207</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1746508586207</updated>
    </task>
    <task id="LOCAL-00012" summary="Applicant Page - Filter Not Working as Expected #301">
      <option name="closed" value="true" />
      <created>1746534085321</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1746534085321</updated>
    </task>
    <task id="LOCAL-00013" summary="updated seo pages api">
      <option name="closed" value="true" />
      <created>1746607623320</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1746607623320</updated>
    </task>
    <task id="LOCAL-00014" summary="implemented permissions">
      <option name="closed" value="true" />
      <created>1746631508704</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1746631508704</updated>
    </task>
    <task id="LOCAL-00015" summary="token is updated only once the webhook confirms success">
      <option name="closed" value="true" />
      <created>1746681235675</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1746681235675</updated>
    </task>
    <task id="LOCAL-00016" summary="Forgot Password - Reset Email Not Received Despite Success Message #309">
      <option name="closed" value="true" />
      <created>1746685652893</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1746685652893</updated>
    </task>
    <task id="LOCAL-00017" summary="Marketing Page - Populate and Send Bulk Email Based on Selected User IDs #311">
      <option name="closed" value="true" />
      <created>1746698107349</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1746698107349</updated>
    </task>
    <task id="LOCAL-00018" summary="added job post limit">
      <option name="closed" value="true" />
      <created>1746711062018</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1746711062019</updated>
    </task>
    <task id="LOCAL-00019" summary="added job post limit logic in jobpost api">
      <option name="closed" value="true" />
      <created>1746776812118</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1746776812119</updated>
    </task>
    <task id="LOCAL-00020" summary="seo-pages-api-search-endpoint">
      <option name="closed" value="true" />
      <created>1746784185811</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1746784185811</updated>
    </task>
    <task id="LOCAL-00021" summary="user api added filter to allusers endpoint&#10;role&#10;email searching &#10;sorting name first and last">
      <option name="closed" value="true" />
      <created>1746794962339</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1746794962339</updated>
    </task>
    <task id="LOCAL-00022" summary="resolved conflict">
      <option name="closed" value="true" />
      <created>1747031193024</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1747031193024</updated>
    </task>
    <task id="LOCAL-00023" summary="added stripe properties to all profiles">
      <option name="closed" value="true" />
      <created>1747037094387</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1747037094387</updated>
    </task>
    <task id="LOCAL-00024" summary="now using title instead of url in seo pages api for creation and search">
      <option name="closed" value="true" />
      <created>1747045723741</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1747045723741</updated>
    </task>
    <task id="LOCAL-00025" summary="title to url">
      <option name="closed" value="true" />
      <created>1747116748277</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1747116748277</updated>
    </task>
    <task id="LOCAL-00026" summary="missing annotation">
      <option name="closed" value="true" />
      <created>1747119314830</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1747119314830</updated>
    </task>
    <task id="LOCAL-00027" summary="&#10;removed fallback code for the title in search endpoint">
      <option name="closed" value="true" />
      <created>1747121168061</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1747121168061</updated>
    </task>
    <task id="LOCAL-00028" summary="added stripe properties in qa and prod profiles&#10;and added @Transactional annotation">
      <option name="closed" value="true" />
      <created>1747126327978</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1747126327978</updated>
    </task>
    <task id="LOCAL-00029" summary="added enterprise plan">
      <option name="closed" value="true" />
      <created>1747140141217</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1747140141217</updated>
    </task>
    <task id="LOCAL-00030" summary="added stripe customer creation on completion  of company profile">
      <option name="closed" value="true" />
      <created>1747198501108</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1747198501108</updated>
    </task>
    <task id="LOCAL-00031" summary="updated checkout accordingly">
      <option name="closed" value="true" />
      <created>1747199325674</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1747199325674</updated>
    </task>
    <task id="LOCAL-00032" summary="added sending list of invoices in response">
      <option name="closed" value="true" />
      <created>1747217099486</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1747217099486</updated>
    </task>
    <task id="LOCAL-00033" summary="added marketing user in schema.sql">
      <option name="closed" value="true" />
      <created>1747282396999</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1747282396999</updated>
    </task>
    <task id="LOCAL-00034" summary="Find jobs &amp; Employer Page - Open Jobs Count Mismatch #264">
      <option name="closed" value="true" />
      <created>1747292873492</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1747292873492</updated>
    </task>
    <task id="LOCAL-00035" summary="paused stripe api">
      <option name="closed" value="true" />
      <created>1747368956540</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1747368956540</updated>
    </task>
    <task id="LOCAL-00036" summary="Company name with space not working">
      <option name="closed" value="true" />
      <created>1747376616297</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1747376616297</updated>
    </task>
    <task id="LOCAL-00037" summary="added marketing user">
      <option name="closed" value="true" />
      <created>1747376789479</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1747376789479</updated>
    </task>
    <task id="LOCAL-00038" summary="uncommented checkJobPostLimit">
      <option name="closed" value="true" />
      <created>1747379388239</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1747379388239</updated>
    </task>
    <task id="LOCAL-00039" summary="added min and max salary">
      <option name="closed" value="true" />
      <created>1747382910953</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1747382910953</updated>
    </task>
    <task id="LOCAL-00040" summary="added pending changes">
      <option name="closed" value="true" />
      <created>1747396419589</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1747396419589</updated>
    </task>
    <task id="LOCAL-00041" summary="added records in department component">
      <option name="closed" value="true" />
      <created>1747401847745</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1747401847745</updated>
    </task>
    <task id="LOCAL-00042" summary="now delete is working and now creating new token after updating the plan">
      <option name="closed" value="true" />
      <created>1747738657826</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1747738657826</updated>
    </task>
    <task id="LOCAL-00043" summary="delete to put">
      <option name="closed" value="true" />
      <created>1747739057451</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1747739057451</updated>
    </task>
    <task id="LOCAL-00044" summary="added message api (except websocket)">
      <option name="closed" value="true" />
      <created>1747824101123</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1747824101123</updated>
    </task>
    <task id="LOCAL-00045" summary="changed update and cancel endpoints in stripe controller api&#10;and candidate experience null">
      <option name="closed" value="true" />
      <created>1748255539933</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1748255539933</updated>
    </task>
    <task id="LOCAL-00046" summary="now generating new token after plan cancellation">
      <option name="closed" value="true" />
      <created>1748260643039</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1748260643039</updated>
    </task>
    <option name="localTasksCounter" value="47" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="resolved conflict" />
    <MESSAGE value="added stripe properties to all profiles" />
    <MESSAGE value="now using title instead of url in seo pages api for creation and search" />
    <MESSAGE value="title to url" />
    <MESSAGE value="missing annotation" />
    <MESSAGE value="&#10;removed fallback code for the title in search endpoint" />
    <MESSAGE value="added stripe properties in qa and prod profiles&#10;and added @Transactional annotation" />
    <MESSAGE value="added enterprise plan" />
    <MESSAGE value="added stripe customer creation on completion  of company profile" />
    <MESSAGE value="updated checkout accordingly" />
    <MESSAGE value="added sending list of invoices in response" />
    <MESSAGE value="added marketing user in schema.sql" />
    <MESSAGE value="Find jobs &amp; Employer Page - Open Jobs Count Mismatch #264" />
    <MESSAGE value="paused stripe api" />
    <MESSAGE value="Company name with space not working" />
    <MESSAGE value="added marketing user" />
    <MESSAGE value="uncommented checkJobPostLimit" />
    <MESSAGE value="added min and max salary" />
    <MESSAGE value="added pending changes" />
    <MESSAGE value="added records in department component" />
    <MESSAGE value="now delete is working and now creating new token after updating the plan" />
    <MESSAGE value="delete to put" />
    <MESSAGE value="added message api (except websocket)" />
    <MESSAGE value="changed update and cancel endpoints in stripe controller api&#10;and candidate experience null" />
    <MESSAGE value="now generating new token after plan cancellation" />
    <option name="LAST_COMMIT_MESSAGE" value="now generating new token after plan cancellation" />
  </component>
</project>